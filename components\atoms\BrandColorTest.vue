<template>
  <div class="p-6 space-y-4">
    <h2 class="text-2xl font-bold text-brand-700">Brand Color Test</h2>
    
    <!-- Test direct Tailwind brand colors -->
    <div class="space-y-2">
      <h3 class="text-lg font-semibold">Direct Tailwind Brand Colors:</h3>
      <div class="flex gap-2 flex-wrap">
        <div class="w-16 h-16 bg-brand-50 border border-gray-300 rounded flex items-center justify-center text-xs">50</div>
        <div class="w-16 h-16 bg-brand-100 border border-gray-300 rounded flex items-center justify-center text-xs">100</div>
        <div class="w-16 h-16 bg-brand-200 border border-gray-300 rounded flex items-center justify-center text-xs">200</div>
        <div class="w-16 h-16 bg-brand-300 border border-gray-300 rounded flex items-center justify-center text-xs">300</div>
        <div class="w-16 h-16 bg-brand-400 border border-gray-300 rounded flex items-center justify-center text-xs text-white">400</div>
        <div class="w-16 h-16 bg-brand-500 border border-gray-300 rounded flex items-center justify-center text-xs text-white">500</div>
        <div class="w-16 h-16 bg-brand-600 border border-gray-300 rounded flex items-center justify-center text-xs text-white">600</div>
        <div class="w-16 h-16 bg-brand-700 border border-gray-300 rounded flex items-center justify-center text-xs text-white">700</div>
        <div class="w-16 h-16 bg-brand-800 border border-gray-300 rounded flex items-center justify-center text-xs text-white">800</div>
        <div class="w-16 h-16 bg-brand-900 border border-gray-300 rounded flex items-center justify-center text-xs text-white">900</div>
        <div class="w-16 h-16 bg-brand-950 border border-gray-300 rounded flex items-center justify-center text-xs text-white">950</div>
      </div>
    </div>

    <!-- Test Nuxt UI components with brand colors -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold">Nuxt UI Components with Brand Colors:</h3>
      
      <!-- Buttons using primary (which should now be brand) -->
      <div class="flex gap-2 flex-wrap">
        <UButton color="primary">Primary Button (Brand)</UButton>
        <UButton color="primary" variant="outline">Primary Outline</UButton>
        <UButton color="primary" variant="subtle">Primary Subtle</UButton>
      </div>

      <!-- Badge -->
      <div class="flex gap-2">
        <UBadge color="primary">Primary Badge</UBadge>
        <UBadge color="primary" variant="outline">Primary Outline Badge</UBadge>
      </div>

      <!-- Alert -->
      <UAlert 
        color="primary" 
        title="Brand Color Alert" 
        description="This alert should use your custom brand colors."
        icon="i-lucide-info"
      />

      <!-- Progress -->
      <UProgress color="primary" :value="75" />
    </div>

    <!-- Test with cn utility -->
    <div class="space-y-2">
      <h3 class="text-lg font-semibold">Using cn() Utility:</h3>
      <div :class="cn('p-4 rounded-lg', 'bg-brand-100 border-2 border-brand-300')">
        Background with brand-100, border with brand-300
      </div>
      <div :class="cn('p-4 rounded-lg text-white', 'bg-brand-600 hover:bg-brand-700 transition-colors')">
        Hover effect with brand colors
      </div>
    </div>
  </div>
</template>

<script setup>
import { cn } from '~/utils/cn'
</script>
