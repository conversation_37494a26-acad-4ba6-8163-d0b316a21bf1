export default defineAppConfig({
  ui: {
    colors: {
      primary: "brand",
      neutral: "zinc",
    },
    button: {
      base: "inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",
      variants: {
        color: {
          primary: "bg-primary text-inverted hover:bg-primary-hover",
          secondary: "bg-secondary text-inverted hover:bg-secondary-hover",
          tertiary: "bg-tertiary text-inverted hover:bg-tertiary-hover",
          neutral: "bg-neutral text-inverted hover:bg-neutral-hover",
          inverted: "bg-inverted text-primary hover:bg-inverted-hover",
        },
        size: {
          xs: "px-3 py-2 text-xs",
          sm: "px-4 py-2 text-sm",
          md: "px-5 py-2.5 text-sm",
          lg: "px-6 py-3 text-base",
          xl: "px-7 py-4 text-base",
        },
      },
      defaultVariants: {
        color: "primary",
        size: "md",
      },
    },
    card: {
      slots: {
        root: "shadow-lg border-1 border-neutral-200 p-4",
      },
      variants: {
        variant: {
          solid: {
            root: "bg-inverted text-inverted",
          },
        },
      },
      defaultVariants: {
        variant: "solid",
      },
    },
    input: {
      slots: {
        root: "relative inline-flex items-center",
        base: [
          "w-full rounded-md border-0 placeholder:text-dimmed focus:outline-none disabled:cursor-not-allowed disabled:opacity-75",
          "transition-colors ",
        ],
        // leading: "absolute inset-y-0 start-0 flex items-center",
        // leadingIcon: "shrink-0 text-dimmed",
        // leadingAvatar: "shrink-0",
        // leadingAvatarSize: "",
        // trailing: "absolute inset-y-0 end-0 flex items-center",
        // trailingIcon: "shrink-0 text-dimmed",
      },
      variants: {
        // size: {
        //   xs: {
        //     base: "px-2 py-1 text-xs gap-1",
        //     leading: "ps-2",
        //     trailing: "pe-2",
        //     leadingIcon: "size-4",
        //     leadingAvatarSize: "3xs",
        //     trailingIcon: "size-4",
        //   },
        //   sm: {
        //     base: "px-2.5 py-1.5 text-xs gap-1.5",
        //     leading: "ps-2.5",
        //     trailing: "pe-2.5",
        //     leadingIcon: "size-4",
        //     leadingAvatarSize: "3xs",
        //     trailingIcon: "size-4",
        //   },
        //   md: {
        //     base: "px-2.5 py-1.5 text-sm gap-1.5",
        //     leading: "ps-2.5",
        //     trailing: "pe-2.5",
        //     leadingIcon: "size-5",
        //     leadingAvatarSize: "2xs",
        //     trailingIcon: "size-5",
        //   },
        //   lg: {
        //     base: "px-3 py-2 text-sm gap-2",
        //     leading: "ps-3",
        //     trailing: "pe-3",
        //     leadingIcon: "size-5",
        //     leadingAvatarSize: "2xs",
        //     trailingIcon: "size-5",
        //   },
        //   xl: {
        //     base: "px-3 py-2 text-base gap-2",
        //     leading: "ps-3",
        //     trailing: "pe-3",
        //     leadingIcon: "size-6",
        //     leadingAvatarSize: "xs",
        //     trailingIcon: "size-6",
        //   },
        // },
        variant: {
          outline: "bg-white border-2 border-neutral-200 text-black",
        },
      },
      compoundVariants: [
        {
          color: "primary",
          variant: ["outline", "subtle"],
          class:
            "focus-visible:ring-2 focus-visible:ring-inset focus-visible:ring-primary",
        },
        {
          color: "primary",
          highlight: true,
          class: "ring ring-inset ring-primary",
        },
        {
          color: "neutral",
          variant: ["outline", "subtle"],
          class:
            "focus-visible:ring-2 focus-visible:ring-inset focus-visible:ring-inverted",
        },
        {
          color: "neutral",
          highlight: true,
          class: "ring ring-inset ring-inverted",
        },
        {
          leading: true,
          size: "xs",
          class: "ps-7",
        },
        {
          leading: true,
          size: "sm",
          class: "ps-8",
        },
        {
          leading: true,
          size: "md",
          class: "ps-9",
        },
        {
          leading: true,
          size: "lg",
          class: "ps-10",
        },
        {
          leading: true,
          size: "xl",
          class: "ps-11",
        },
        {
          trailing: true,
          size: "xs",
          class: "pe-7",
        },
        {
          trailing: true,
          size: "sm",
          class: "pe-8",
        },
        {
          trailing: true,
          size: "md",
          class: "pe-9",
        },
        {
          trailing: true,
          size: "lg",
          class: "pe-10",
        },
        {
          trailing: true,
          size: "xl",
          class: "pe-11",
        },
        {
          loading: true,
          leading: true,
          class: {
            leadingIcon: "animate-spin",
          },
        },
        {
          loading: true,
          leading: false,
          trailing: true,
          class: {
            trailingIcon: "animate-spin",
          },
        },
      ],
      defaultVariants: {
        size: "md",
        color: "primary",
        variant: "outline",
      },
    },
  },
});
